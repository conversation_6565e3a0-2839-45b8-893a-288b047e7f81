.app-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--surface-color);
}

.app-main {
  flex: 1;
  padding: var(--spacing-lg) var(--spacing-md);
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
}

/* Page Header Styles */
.page-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
  position: relative;
}

.page-header::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background-color: var(--primary-color);
  border-radius: var(--border-radius-sm);
}

.page-title {
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
  font-size: 2.5rem;
  font-weight: 800;
  letter-spacing: -0.5px;
}

.page-description {
  color: var(--text-secondary);
  max-width: 700px;
  margin: 0 auto;
  font-size: 1.1rem;
  line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .app-main {
    padding: var(--spacing-md) var(--spacing-sm);
  }

  .page-title {
    font-size: 2rem;
  }

  .page-description {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 1.8rem;
  }

  .page-header {
    margin-bottom: var(--spacing-lg);
  }
}
