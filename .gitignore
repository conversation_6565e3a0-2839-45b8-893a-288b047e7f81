# Combined .gitignore file for the entire project

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.hypothesis/
cover/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
pnpm-debug.log*
.npm
.eslintcache
.node_repl_history
.yarn-integrity
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log

# Distribution directories
dist/
dist-ssr/
*.local

# IDEs and editors
.idea/
.vscode/*
!.vscode/extensions.json
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Project specific files to exclude (as requested)
# Backend specific
backend/static/Dataset/
backend/static/model.keras
backend/static/features_list.pkl
backend/static/image_paths.pkl
backend/static/old_image_path.pkl
backend/static/old_image_paths.pkl
backend/logs/
backend/__pycache__/
backend/controllers/__pycache__/
backend/models/__pycache__/
backend/utils/__pycache__/
backend/views/__pycache__/

# Frontend specific
frontend/node_modules/
frontend/dist/
frontend/dist-ssr/
frontend/*.local

# Train model specific
train_model/Dataset/
train_model/mobile_pic/
train_model/model.keras
train_model/features_list.pkl
train_model/image_paths.pkl
train_model/*.pkl
train_model/trainenv/
train_model/__pycache__/
