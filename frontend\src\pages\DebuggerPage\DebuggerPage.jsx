import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import './DebuggerPage.css';

const DebuggerPage = () => {
  const [logs, setLogs] = useState([]);
  const [isLive, setIsLive] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    level: '',
    search: '',
    lines: 100
  });
  const [autoScroll, setAutoScroll] = useState(true);
  const [lastTimestamp, setLastTimestamp] = useState(null);
  
  const logsContainerRef = useRef(null);
  const intervalRef = useRef(null);

  // API base URL
  const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000';

  // Fetch logs from backend
  const fetchLogs = async (isLiveUpdate = false) => {
    try {
      if (!isLiveUpdate) setLoading(true);
      setError(null);

      const params = new URLSearchParams();
      if (filters.lines) params.append('lines', filters.lines);
      if (filters.level) params.append('level', filters.level);
      if (filters.search) params.append('search', filters.search);

      const endpoint = isLive ? '/logs/live' : '/logs';
      const response = await fetch(`${API_BASE_URL}${endpoint}?${params}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        setLogs(data.logs);
        if (data.timestamp) {
          setLastTimestamp(data.timestamp);
        }
        
        // Auto scroll to bottom if enabled
        if (autoScroll && logsContainerRef.current) {
          setTimeout(() => {
            logsContainerRef.current.scrollTop = logsContainerRef.current.scrollHeight;
          }, 100);
        }
      } else {
        setError(data.message || 'Failed to fetch logs');
      }
    } catch (err) {
      setError(`Error fetching logs: ${err.message}`);
      console.error('Error fetching logs:', err);
    } finally {
      if (!isLiveUpdate) setLoading(false);
    }
  };

  // Clear logs
  const clearLogs = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${API_BASE_URL}/logs/clear`, {
        method: 'POST'
      });
      
      const data = await response.json();
      
      if (data.success) {
        setLogs([]);
        alert('Logs cleared successfully!');
      } else {
        setError(data.message || 'Failed to clear logs');
      }
    } catch (err) {
      setError(`Error clearing logs: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Start/stop live monitoring
  const toggleLiveMode = () => {
    if (isLive) {
      // Stop live mode
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      setIsLive(false);
    } else {
      // Start live mode
      setIsLive(true);
      fetchLogs(true); // Initial fetch
      intervalRef.current = setInterval(() => {
        fetchLogs(true);
      }, 2000); // Update every 2 seconds
    }
  };

  // Handle filter changes
  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // Apply filters
  const applyFilters = () => {
    fetchLogs();
  };

  // Get log level color
  const getLogLevelColor = (level) => {
    switch (level?.toUpperCase()) {
      case 'ERROR': return '#ff4757';
      case 'WARNING': return '#ffa502';
      case 'INFO': return '#3742fa';
      case 'DEBUG': return '#2ed573';
      default: return '#747d8c';
    }
  };

  // Format timestamp
  const formatTimestamp = (timestamp) => {
    if (!timestamp) return '';
    try {
      return new Date(timestamp).toLocaleString();
    } catch {
      return timestamp;
    }
  };

  // Initial load
  useEffect(() => {
    fetchLogs();
    
    // Cleanup on unmount
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  return (
    <motion.div 
      className="debugger-page"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="debugger-header">
        <h1>Live Log Debugger</h1>
        <p>Monitor real-time logs from the SBIR API</p>
      </div>

      {/* Controls */}
      <div className="debugger-controls">
        <div className="control-group">
          <label>Lines:</label>
          <select 
            value={filters.lines} 
            onChange={(e) => handleFilterChange('lines', e.target.value)}
          >
            <option value={50}>50</option>
            <option value={100}>100</option>
            <option value={200}>200</option>
            <option value={500}>500</option>
          </select>
        </div>

        <div className="control-group">
          <label>Level:</label>
          <select 
            value={filters.level} 
            onChange={(e) => handleFilterChange('level', e.target.value)}
          >
            <option value="">All</option>
            <option value="INFO">INFO</option>
            <option value="ERROR">ERROR</option>
            <option value="WARNING">WARNING</option>
            <option value="DEBUG">DEBUG</option>
          </select>
        </div>

        <div className="control-group">
          <label>Search:</label>
          <input
            type="text"
            value={filters.search}
            onChange={(e) => handleFilterChange('search', e.target.value)}
            placeholder="Search logs..."
          />
        </div>

        <div className="control-buttons">
          <button onClick={applyFilters} disabled={loading}>
            {loading ? 'Loading...' : 'Apply Filters'}
          </button>
          
          <button 
            onClick={toggleLiveMode} 
            className={isLive ? 'live-active' : ''}
          >
            {isLive ? '⏸️ Stop Live' : '▶️ Start Live'}
          </button>

          <button onClick={clearLogs} disabled={loading} className="clear-btn">
            🗑️ Clear Logs
          </button>
        </div>

        <div className="control-group">
          <label>
            <input
              type="checkbox"
              checked={autoScroll}
              onChange={(e) => setAutoScroll(e.target.checked)}
            />
            Auto Scroll
          </label>
        </div>
      </div>

      {/* Status */}
      <div className="debugger-status">
        <span className={`status-indicator ${isLive ? 'live' : 'stopped'}`}>
          {isLive ? '🟢 Live' : '🔴 Stopped'}
        </span>
        <span>Total Logs: {logs.length}</span>
        {lastTimestamp && (
          <span>Last Update: {new Date(lastTimestamp * 1000).toLocaleTimeString()}</span>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <div className="error-message">
          <strong>Error:</strong> {error}
        </div>
      )}

      {/* Logs Container */}
      <div className="logs-container" ref={logsContainerRef}>
        {logs.length === 0 ? (
          <div className="no-logs">
            {loading ? 'Loading logs...' : 'No logs found'}
          </div>
        ) : (
          logs.map((log, index) => (
            <motion.div
              key={index}
              className="log-entry"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: index * 0.01 }}
            >
              <div className="log-header">
                <span className="log-timestamp">
                  {formatTimestamp(log.timestamp)}
                </span>
                <span 
                  className="log-level"
                  style={{ color: getLogLevelColor(log.level) }}
                >
                  {log.level}
                </span>
                <span className="log-location">
                  {log.filename}:{log.line_number}
                </span>
              </div>
              <div className="log-message">
                {log.message}
              </div>
            </motion.div>
          ))
        )}
      </div>
    </motion.div>
  );
};

export default DebuggerPage;
