/* ===== UPLOAD PAGE STYLES ===== */

.upload-page {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.page-header {
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.page-title {
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
}

.page-description {
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

/* Upload Container */
.upload-container {
  display: flex;
  justify-content: center;
  width: 100%;
}

.upload-card {
  width: 100%;
  max-width: 600px;
}

.action-buttons {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

@media (max-width: 576px) {
  .action-buttons {
    flex-direction: column;
  }
}

/* Results Container */
.results-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  width: 100%;
}

/* Loading Wrapper */
.loading-wrapper {
  padding: var(--spacing-xl);
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Result Card Styles */
.result-card {
  overflow: hidden;
}

.result-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
}

.result-image-container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: var(--spacing-md);
}

.result-image {
  max-width: 100%;
  max-height: 400px;
  object-fit: contain;
  border-radius: var(--border-radius-md);
}

.download-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
}

/* Image Grid Styles */
.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--spacing-md);
  width: 100%;
}

.image-box {
  aspect-ratio: 1;
  border-radius: var(--border-radius-md);
  overflow: hidden;
  background-color: var(--surface-lighter);
  box-shadow: var(--shadow-sm);
  transition: var(--transition-normal);
}

.image-box:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-md);
}

.image-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.retrieved-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition-normal);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  opacity: 0;
  transition: var(--transition-normal);
  padding: var(--spacing-sm);
}

.image-box:hover .image-overlay {
  opacity: 1;
}

.similarity-badge {
  background: rgba(39, 88, 107, 0.8);
  color: var(--text-primary);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
  align-self: flex-start;
}

.download-btn-small {
  background: var(--primary-color);
  border: none;
  border-radius: var(--border-radius-sm);
  color: var(--text-primary);
  cursor: pointer;
  transition: var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xs);
  align-self: flex-end;
}

.download-btn-small:hover {
  background: var(--primary-light);
  transform: scale(1.05);
}

/* Empty State */
.empty-state {
  min-height: 300px;
  margin-top: var(--spacing-md);
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .image-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .image-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 576px) {
  .image-grid {
    grid-template-columns: repeat(1, 1fr);
  }

  .action-buttons {
    flex-direction: column;
  }
}

