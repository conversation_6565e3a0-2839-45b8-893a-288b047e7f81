.app-header {
  background-color: var(--surface-light);
  box-shadow: var(--shadow-md);
  border-bottom: 1px solid var(--primary-dark);
  padding: var(--spacing-md) var(--spacing-lg);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.logo-container {
  cursor: pointer;
  transition: var(--transition-normal);
}

.logo {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin: 0;
  letter-spacing: 0.5px;
  transition: var(--transition-normal);
}

.logo:hover {
  color: var(--primary-light);
}

.nav-links {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
  flex-wrap: wrap;
}

.nav-btn {
  background: transparent;
  color: var(--text-secondary);
  border: none;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-md);
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 500;
  transition: var(--transition-normal);
}

.nav-btn:hover {
  background-color: rgba(39, 88, 107, 0.1);
  color: var(--text-primary);
}

.nav-btn.active {
  background-color: var(--primary-color);
  color: var(--text-primary);
}

.menu-toggle {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  width: 40px;
  height: 40px;
  position: relative;
  z-index: 101;
}

.menu-icon {
  display: block;
  position: relative;
  width: 24px;
  height: 2px;
  background-color: var(--primary-color);
  margin: 0 auto;
  transition: background-color 0.3s ease;
}

.menu-icon::before,
.menu-icon::after {
  content: '';
  position: absolute;
  width: 24px;
  height: 2px;
  background-color: var(--primary-color);
  transition: transform 0.3s ease;
}

.menu-icon::before {
  top: -8px;
}

.menu-icon::after {
  bottom: -8px;
}

.menu-icon.open {
  background-color: transparent;
}

.menu-icon.open::before {
  transform: rotate(45deg);
  top: 0;
}

.menu-icon.open::after {
  transform: rotate(-45deg);
  bottom: 0;
}

/* Responsive Design */
@media (max-width: 992px) {
  .header-content {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    position: relative;
  }

  .menu-toggle {
    display: block;
  }

  .nav-links {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: var(--surface-light);
    width: 250px;
    flex-direction: column;
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-lg);
    transform: translateY(-20px);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 100;
  }

  .nav-links.open {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }

  .nav-btn {
    width: 100%;
    text-align: left;
    padding: var(--spacing-md);
    border-radius: var(--border-radius-sm);
  }
}

@media (max-width: 480px) {
  .logo {
    font-size: 1.2rem;
  }

  .nav-links {
    width: 100%;
    right: 0;
    left: 0;
  }
}
