/* Edit Image Page Styles */
.edit-image-page {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
  min-height: 100vh;
}

/* Main Content - Parallel Layout */
.edit-container {
  display: flex;
  flex-direction: column;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
  padding: 0 var(--spacing-lg);
}

/* Upload Section */
.upload-section {
  width: 100%;
  margin-bottom: var(--spacing-xl);
  display: flex;
  justify-content: center;
  align-items: center;
}

.upload-card {
  width: 100%;
  max-width: 700px;
  padding: var(--spacing-xl);
}

.upload-area {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 2px dashed var(--primary-light);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
  min-height: 400px;
  background-color: var(--surface-lighter);
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: var(--spacing-md);
  overflow: hidden;
}

.upload-area:hover {
  border-color: var(--primary-color);
  background-color: rgba(39, 88, 107, 0.05);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* Page-specific upload instruction styles */
.upload-instruction {
  font-size: 1.1rem;
}

.upload-subtext {
  max-width: 300px;
}

.upload-preview {
  max-width: 100%;
  max-height: 400px;
  border-radius: var(--border-radius-sm);
  object-fit: contain;
  box-shadow: var(--shadow-md);
  width: auto;
  height: auto;
}

/* Parallel Edit Section */
.edit-section {
  display: flex;
  flex-direction: row;
  gap: var(--spacing-xl);
  width: 100%;
  margin-top: var(--spacing-lg);
}

/* Edit Controls */
.edit-card {
  flex: 1;
  min-width: 300px;
  padding: var(--spacing-xl);
}

.edit-controls {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.edit-control {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-md);
}

.edit-control label {
  display: flex;
  justify-content: space-between;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.edit-control input[type="range"] {
  width: 100%;
  height: 6px;
  -webkit-appearance: none;
  appearance: none;
  background: linear-gradient(to right, var(--primary-color), var(--primary-light));
  border-radius: var(--border-radius-full);
  outline: none;
  cursor: pointer;
  opacity: 0.9;
  transition: opacity 0.2s;
}

.edit-control input[type="range"]:hover {
  opacity: 1;
}

.edit-control input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: var(--primary-color);
  border: 2px solid white;
  cursor: pointer;
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
}

.edit-control input[type="range"]::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-md);
}

.edit-control span {
  font-size: 0.9rem;
  color: var(--text-secondary);
  text-align: right;
}

/* Preview Section */
.preview-card {
  flex: 1;
  min-width: 300px;
  padding: var(--spacing-xl);
  display: flex;
  flex-direction: column;
}

.preview-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-md);
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: var(--border-radius-md);
  min-height: 400px;
  margin-bottom: var(--spacing-md);
  overflow: hidden;
}

.edited-preview {
  max-width: 100%;
  max-height: 400px;
  border-radius: var(--border-radius-sm);
  object-fit: contain;
  box-shadow: var(--shadow-md);
  width: 500px;
  height: auto;
}

.preview-placeholder {
  color: var(--text-secondary);
  font-size: 1rem;
  text-align: center;
  padding: var(--spacing-md);
}

.preview-actions {
  display: flex;
  justify-content: center;
  margin-top: auto;
}

.action-button svg {
  width: 18px;
  height: 18px;
}

/* Background Buttons */
.bg-buttons-container {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-md);
  justify-content: center;
}

.bg-button {
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
}

.bg-button:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.bg-button:disabled {
  background-color: var(--surface-lighter);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Responsive Design */
@media (max-width: 992px) {
  .edit-section {
    flex-direction: column;
  }

  .edit-card, .preview-card {
    width: 100%;
  }

  .preview-container {
    min-height: 350px;
  }

  .edited-preview {
    max-height: 350px;
  }
}

@media (max-width: 768px) {
  .bg-buttons-container {
    flex-direction: column;
    align-items: center;
  }

  .bg-button {
    width: 100%;
    max-width: 300px;
  }
}

@media (max-width: 576px) {
  .upload-area {
    padding: var(--spacing-md);
    min-height: 300px;
  }

  .upload-preview,
  .edited-preview {
    max-height: 300px;
  }

  .edit-card, .preview-card {
    padding: var(--spacing-md);
  }
}