/* Homepage Styles - Simplified */
.homepage {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: var(--spacing-xl) 0;
  gap: var(--spacing-xl);
  background: var(--surface-color);
}

/* Hero Section - Simplified */
.hero-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-xl);
  padding: var(--spacing-xl) var(--spacing-lg);
  max-width: 1200px;
  margin: 0 auto;
}

.hero-left {
  flex: 1;
  max-width: 600px;
}

.hero-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: var(--spacing-md);
  line-height: 1.2;
  color: var(--text-primary);
  position: relative;
}

.hero-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 160px;
    width: 263px;
  height: 3px;
  background-color: var(--primary-color);
  border-radius: var(--border-radius-sm);
}

.highlight {
  color: var(--primary-color);
}

.hero-description {
  font-size: 1.2rem;
  line-height: 1.6;
  margin-bottom: var(--spacing-lg);
  color: var(--text-secondary);
}

.hero-buttons {
  display: flex;
  gap: var(--spacing-md);
}

.btn-lg {
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: 1rem;
  font-weight: 600;
  border-radius: var(--border-radius-md);
  transition: all 0.3s ease;
}

.hero-right {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.hero-image {
  max-width: 400px;
  height: auto;
  border-radius: 12px;
  box-shadow: var(--shadow-md);
  border: 3px solid var(--primary-color);
}

/* Design Tools Section - Simplified */
.features-section {
  padding: var(--spacing-lg);
  max-width: 1200px;
  margin: 0 auto;
}

.section-title {
  text-align: center;
  font-size: 2rem;
  margin-bottom: var(--spacing-lg);
  color: var(--primary-color);
  font-weight: 700;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: var(--spacing-lg);
  max-width: 1000px;
  margin: 0 auto;
}

.feature-card {
  background-color: var(--surface-light);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  box-shadow: var(--shadow-sm);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid var(--surface-lighter);
  height: 100%;
  min-height: 280px;
  justify-content: center;
}

.feature-card:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
  transform: translateY(-5px);
}

.feature-icon {
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
  background-color: rgba(39, 88, 107, 0.1);
  padding: var(--spacing-sm);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 70px;
  height: 70px;
}

.feature-title {
  font-size: 1.3rem;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
  font-weight: 600;
}

.feature-description {
  color: var(--text-secondary);
  line-height: 1.4;
  font-size: 0.95rem;
  max-width: 90%;
  margin: 0 auto;
}
/* HomePage-specific responsive styles */
@media (max-width: 992px) {
  .hero-content {
    flex-direction: column-reverse;
    text-align: center;
    padding: var(--spacing-md);
  }

  .hero-left {
    max-width: 100%;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-title::after {
    left: 50%;
    transform: translateX(-50%);
  }

  .hero-buttons {
    justify-content: center;
  }

  .hero-image {
    margin-bottom: var(--spacing-md);
    max-width: 350px;
  }

  .features-grid {
    gap: var(--spacing-md);
  }

  .feature-card {
    min-height: 250px;
  }
}

@media (max-width: 768px) {
  .features-grid {
    grid-template-columns: 1fr;
    grid-template-rows: auto;
    max-width: 500px;
    margin: 0 auto;
  }
}

@media (max-width: 576px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-description {
    font-size: 1rem;
  }

  .hero-buttons {
    flex-direction: column;
    width: 100%;
  }

  .btn-lg {
    width: 100%;
  }

  .hero-image {
    max-width: 250px;
  }
}