/* Image Box - Container for the image card */
.image-box {
    margin-bottom: var(--spacing-md);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    background-color: var(--surface-light);
    box-shadow: var(--shadow-md);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
}

/* Image Container - Holds the image and overlay */
.image-container {
    position: relative;
    width: 100%;
    aspect-ratio: 1 / 1;
    overflow: hidden;
    border-radius: var(--border-radius-md);
    background-color: var(--surface-light);
}

/* Retrieved Image - The actual image */
.retrieved-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    background-color: var(--surface-light);
    transition: transform 0.3s ease;
    position: relative;
    z-index: 1;
}

/* Image Overlay - Contains badges and buttons */
.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    padding: var(--spacing-sm);
    background: linear-gradient(to bottom, rgba(0,0,0,0.3) 0%, transparent 30%, transparent 70%, rgba(0,0,0,0.3) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 2;
}

.image-container:hover .image-overlay {
    opacity: 1;
}

/* Similarity Badge */
.similarity-badge {
    background-color: var(--primary-color);
    color: white;
    padding: 4px 8px;
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-bold);
    box-shadow: var(--shadow-sm);
    align-self: flex-start;
    z-index: 3;
}

/* Download Button */
.download-btn-small {
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s ease;
    align-self: flex-start;
    z-index: 3;
}

.download-btn-small:hover {
    background-color: var(--primary-color);
}

/* Image Error State */
.image-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    padding: var(--spacing-md);
    color: var(--text-secondary);
    text-align: center;
    background-color: var(--surface-light);
}

.image-error .error-icon {
    font-size: 24px;
    margin-bottom: var(--spacing-sm);
    color: var(--color-warning);
}

/* Loading Animation - only shown when loading class is present */
.image-container.loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, var(--surface-light) 0%, var(--surface-lighter) 50%, var(--surface-light) 100%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    opacity: 0.8;
    z-index: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .similarity-badge {
        font-size: 9px;
        padding: 3px 6px;
    }

    .download-btn-small {
        width: 28px;
        height: 28px;
    }
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}
