.slider-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-bottom: var(--spacing-xs);
}

.slider-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xs);
}

.slider-label {
  font-size: var(--font-size-sm);
  color: var(--text-color);
  font-weight: var(--font-weight-medium);
}

.slider-value {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
  min-width: 30px;
  text-align: right;
}

.slider-input {
  -webkit-appearance: none;
  width: 100%;
  height: 4px;
  background: var(--surface-light);
  border-radius: var(--border-radius-full);
  outline: none;
  margin: 4px 0;
}

.slider-input::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 14px;
  height: 14px;
  background: var(--primary-color);
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
}

.slider-input::-moz-range-thumb {
  width: 14px;
  height: 14px;
  background: var(--primary-color);
  border-radius: 50%;
  cursor: pointer;
  border: none;
  transition: all 0.2s ease;
}

.slider-input::-webkit-slider-thumb:hover {
  transform: scale(1.2);
}

.slider-input::-moz-range-thumb:hover {
  transform: scale(1.2);
}

.slider-ticks {
  display: flex;
  justify-content: space-between;
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  margin-top: 2px;
}
