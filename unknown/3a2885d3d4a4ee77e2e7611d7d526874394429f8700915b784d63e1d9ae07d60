import React from 'react';
import './styles.css';

function ErrorMessage({ message, onDismiss }) {
  return (
    <div className="error-message-container">
      <div className="error-icon">
        <svg viewBox="0 0 24 24" width="24" height="24">
          <path 
            fill="currentColor" 
            d="M12,2C17.53,2 22,6.47 22,12C22,17.53 17.53,22 12,22C6.47,22 2,17.53 2,12C2,6.47 6.47,2 12,2M15.59,7L12,10.59L8.41,7L7,8.41L10.59,12L7,15.59L8.41,17L12,13.41L15.59,17L17,15.59L13.41,12L17,8.41L15.59,7Z" 
          />
        </svg>
      </div>
      <p className="error-text">{message}</p>
      {onDismiss && (
        <button className="error-dismiss" onClick={onDismiss}>
          <svg viewBox="0 0 24 24" width="16" height="16">
            <path 
              fill="currentColor" 
              d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" 
            />
          </svg>
        </button>
      )}
    </div>
  );
}

export default ErrorMessage;
