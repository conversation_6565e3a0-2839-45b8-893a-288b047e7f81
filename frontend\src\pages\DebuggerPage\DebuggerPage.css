.debugger-page {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
  font-family: 'Courier New', monospace;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  min-height: 100vh;
  color: #ffffff;
}

.debugger-header {
  text-align: center;
  margin-bottom: 30px;
}

.debugger-header h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.debugger-header p {
  font-size: 1.1rem;
  opacity: 0.8;
  margin: 0;
}

.debugger-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.control-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-group label {
  font-weight: 600;
  font-size: 0.9rem;
  white-space: nowrap;
}

.control-group select,
.control-group input[type="text"] {
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  font-size: 0.9rem;
  min-width: 120px;
}

.control-group select:focus,
.control-group input[type="text"]:focus {
  outline: none;
  border-color: #4ecdc4;
  box-shadow: 0 0 0 2px rgba(78, 205, 196, 0.3);
}

.control-group input[type="text"]::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.control-buttons {
  display: flex;
  gap: 10px;
  margin-left: auto;
}

.control-buttons button {
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.control-buttons button:first-child {
  background: linear-gradient(45deg, #4ecdc4, #44a08d);
  color: white;
}

.control-buttons button:first-child:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(78, 205, 196, 0.4);
}

.control-buttons button:first-child:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.control-buttons .live-active {
  background: linear-gradient(45deg, #ff6b6b, #ee5a52);
  color: white;
}

.control-buttons button:not(.live-active):nth-child(2) {
  background: linear-gradient(45deg, #2ecc71, #27ae60);
  color: white;
}

.control-buttons button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.control-buttons .clear-btn {
  background: linear-gradient(45deg, #e74c3c, #c0392b);
  color: white;
}

.debugger-status {
  display: flex;
  gap: 20px;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 12px 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  font-size: 0.9rem;
  backdrop-filter: blur(10px);
}

.status-indicator {
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
}

.status-indicator.live {
  background: rgba(46, 204, 113, 0.2);
  color: #2ecc71;
}

.status-indicator.stopped {
  background: rgba(231, 76, 60, 0.2);
  color: #e74c3c;
}

.error-message {
  background: rgba(231, 76, 60, 0.2);
  border: 1px solid #e74c3c;
  color: #ffffff;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  font-size: 0.9rem;
}

.logs-container {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  height: 600px;
  overflow-y: auto;
  padding: 20px;
  backdrop-filter: blur(10px);
}

.logs-container::-webkit-scrollbar {
  width: 8px;
}

.logs-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.logs-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

.logs-container::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

.no-logs {
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
  font-size: 1.1rem;
  padding: 40px;
}

.log-entry {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  margin-bottom: 12px;
  padding: 12px 16px;
  transition: all 0.3s ease;
}

.log-entry:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateX(4px);
}

.log-header {
  display: flex;
  gap: 15px;
  align-items: center;
  margin-bottom: 8px;
  font-size: 0.85rem;
  opacity: 0.8;
}

.log-timestamp {
  color: #4ecdc4;
  font-weight: 600;
  min-width: 180px;
}

.log-level {
  font-weight: 700;
  padding: 2px 6px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  min-width: 60px;
  text-align: center;
}

.log-location {
  color: #ffa502;
  font-weight: 500;
}

.log-message {
  color: #ffffff;
  font-size: 0.9rem;
  line-height: 1.4;
  word-break: break-word;
  padding-left: 8px;
  border-left: 3px solid rgba(78, 205, 196, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .debugger-page {
    padding: 15px;
  }
  
  .debugger-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .control-buttons {
    margin-left: 0;
    justify-content: center;
  }
  
  .debugger-status {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }
  
  .log-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
  
  .log-timestamp {
    min-width: auto;
  }
  
  .logs-container {
    height: 400px;
  }
}

/* Animation for new log entries */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.log-entry {
  animation: slideIn 0.3s ease-out;
}
