.app-footer {
  background-color: var(--surface-light);
  border-top: 1px solid var(--primary-dark);
  padding: var(--spacing-lg) 0 var(--spacing-md);
  margin-top: auto;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  gap: var(--spacing-xl);
}

.footer-section {
  flex: 1;
  min-width: 250px;
}

.footer-title {
  color: var(--primary-color);
  font-size: 1.5rem;
  margin-bottom: var(--spacing-md);
}

.footer-subtitle {
  color: var(--primary-light);
  font-size: 1.1rem;
  margin-bottom: var(--spacing-md);
}

.footer-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--spacing-md);
}

.footer-list, .footer-nav {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-list li, .footer-nav li {
  margin-bottom: var(--spacing-sm);
  color: var(--text-secondary);
}

.footer-nav button {
  background: none;
  border: none;
  color: var(--primary-light);
  padding: 0;
  font-size: 1rem;
  cursor: pointer;
  transition: var(--transition-normal);
  text-align: left;
}

.footer-nav button:hover {
  color: var(--accent-color);
  text-decoration: underline;
}

.footer-link {
  color: var(--primary-light);
  text-decoration: none;
  transition: var(--transition-normal);
}

.footer-link:hover {
  color: var(--accent-color);
  text-decoration: underline;
}

.footer-bottom {
  border-top: 1px solid var(--surface-lighter);
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-md);
  text-align: center;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--spacing-lg);
  padding-right: var(--spacing-lg);
}

.copyright {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .footer-content {
    flex-direction: column;
    gap: var(--spacing-lg);
  }

  .footer-section {
    width: 100%;
  }
}
