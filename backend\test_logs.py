#!/usr/bin/env python3
"""
Simple script to generate test logs for the debugger interface
"""

import time
import requests
from utils.logger import logger

def generate_test_logs():
    """Generate various types of log entries for testing the debugger"""
    
    logger.info("Starting log generation test")
    
    # Generate different types of logs
    for i in range(5):
        logger.info(f"Test log entry {i+1} - INFO level")
        time.sleep(1)
        
        if i % 2 == 0:
            logger.warning(f"Test warning {i+1} - WARNING level")
        
        if i == 3:
            logger.error(f"Test error {i+1} - ERROR level")
        
        logger.debug(f"Test debug {i+1} - DEBUG level")
        
        # Make a request to generate API logs
        try:
            response = requests.get('http://localhost:5000/')
            logger.info(f"API request {i+1} completed with status: {response.status_code}")
        except Exception as e:
            logger.error(f"API request {i+1} failed: {str(e)}")
        
        time.sleep(2)
    
    logger.info("Log generation test completed")

if __name__ == "__main__":
    generate_test_logs()
