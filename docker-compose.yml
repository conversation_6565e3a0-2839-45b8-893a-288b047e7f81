services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: designflow-backend
    restart: always
    environment:
      - FLASK_ENV=production
    ports:
      - "5000:5000"
    networks:
      - app-network

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: designflow-frontend
    restart: always
    ports:
      - "3000:80"
    depends_on:
      - backend
    networks:
      - app-network

networks:
  app-network:
    driver: bridge
